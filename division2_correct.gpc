/*
 * The Division 2 - Correct GPC Structure
 * Version: 1.1 (Following GPC Core Features)
 * Author: Based on XBL Jedi Style
 * 
 * FEATURES:
 * - Anti-Recoil (Low/High Modi)
 * - Rapid Fire
 * - Aim Assist
 * - CutStrafe (Zick-Zack Movement)
 * - LED Feedback
 * - Intuitive Commands
 */

// ===== DEFINES (C Language Syntax) =====
define HOME  = 0;
define VIEW  = 1;
define MENU  = 2;
define RB    = 3;
define RT    = 4;
define RS    = 5;
define LB    = 6;
define LT    = 7;
define LS    = 8;
define RX    = 9;
define RY    = 10;
define LX    = 11;
define LY    = 12;
define UP    = 13;
define DOWN  = 14;
define LEFT  = 15;
define RIGHT = 16;
define Y     = 17;
define B     = 18;
define A     = 19;
define X     = 20;

// LED Colors
define Red    = 2;
define Green  = 3;
define Pink   = 4;
define Yellow = 6;

// ===== VARIABLES (C Language Syntax) =====
int anti_recoil_active = TRUE;
int rapid_fire_active = TRUE;
int aim_assist_active = TRUE;
int cutstrafe_active = FALSE;
int anti_recoil_mode = 0; // 0 = Low, 1 = High

// Anti-Recoil Values
int ar_low_vertical = 25;
int ar_high_vertical = 40;
int ar_low_horizontal = 12;
int ar_high_horizontal = 20;

// Rapid Fire Settings
int fire_rate = 14;
int hold_time = 20;
int rest_time = 15;

// CutStrafe Settings
int cutstrafe_direction = 1;
int cutstrafe_timer = 0;
int cutstrafe_speed = 80;
int cutstrafe_delay = 120;

// Aim Assist Settings
int aim_strength = 20;
int aim_timer = 0;

// System Variables
int rumble_count = 0;
int rumble_timer = 0;
int current_vertical = 25;
int current_horizontal = 12;

// ===== INIT FUNCTION (Runs once when script starts) =====
init {
    // Initialize LED to show current state
    update_led_status();
}

// ===== MAIN FUNCTION (Core of script - loops continuously) =====
main {
    // ===== EVENT HANDLERS (Respond to specific events) =====
    
    // Anti-Recoil Mode Toggle: LT + DOWN
    if(get_val(LT) && event_press(DOWN)) {
        anti_recoil_mode = !anti_recoil_mode;
        update_recoil_values();
        rumble_count = anti_recoil_mode + 1; // 1 or 2 rumbles
        update_led_status();
    }
    
    // Rapid Fire Toggle: LT + X
    if(get_val(LT) && event_press(X)) {
        rapid_fire_active = !rapid_fire_active;
        combo_run(single_rumble);
        update_led_status();
    }
    
    // Aim Assist Toggle: LT + Y
    if(get_val(LT) && event_press(Y)) {
        aim_assist_active = !aim_assist_active;
        combo_run(single_rumble);
    }
    
    // CutStrafe Toggle: LT + RIGHT
    if(get_val(LT) && event_press(RIGHT)) {
        cutstrafe_active = !cutstrafe_active;
        combo_run(single_rumble);
    }
    
    // ===== CORE FEATURES (C Language Control Structures) =====
    
    // Anti-Recoil System (if control structure)
    if(anti_recoil_active && get_val(LT) && get_val(RT)) {
        // Vertical Anti-Recoil
        set_val(RY, get_val(RY) + current_vertical);
        
        // Horizontal Anti-Recoil (only when minimal movement)
        if(abs(get_val(RX)) < 25) {
            set_val(RX, get_val(RX) + current_horizontal);
        }
    }
    
    // Rapid Fire System
    if(rapid_fire_active && get_val(RT)) {
        combo_run(rapid_fire_combo);
    }
    
    // Aim Assist System
    if(aim_assist_active && get_val(LT) && !get_val(RT)) {
        if(aim_timer <= 0) {
            combo_run(aim_assist_combo);
            aim_timer = 40;
        }
    }
    
    // CutStrafe System (Zick-Zack Movement)
    if(cutstrafe_active && get_val(LT)) {
        if(cutstrafe_timer <= 0) {
            if(cutstrafe_direction == 1) {
                set_val(LX, cutstrafe_speed);
                cutstrafe_direction = -1;
            } else {
                set_val(LX, -cutstrafe_speed);
                cutstrafe_direction = 1;
            }
            cutstrafe_timer = cutstrafe_delay;
        }
    }
    
    // Auto Sprint Feature
    if(abs(get_val(LY)) > 85) {
        set_val(LS, 100);
    }
    
    // ===== TIMER UPDATES (while-like continuous operation) =====
    if(aim_timer > 0) aim_timer = aim_timer - get_rtime();
    if(cutstrafe_timer > 0) cutstrafe_timer = cutstrafe_timer - get_rtime();
    
    // Handle Rumble Feedback
    handle_rumble_system();
}

// ===== FUNCTIONS (C Language Syntax) =====
function update_recoil_values() {
    if(anti_recoil_mode == 0) {
        current_vertical = ar_low_vertical;
        current_horizontal = ar_low_horizontal;
    } else {
        current_vertical = ar_high_vertical;
        current_horizontal = ar_high_horizontal;
    }
}

function update_led_status() {
    // LED Logic (like XBL Jedi style)
    if(rapid_fire_active && anti_recoil_active) {
        if(anti_recoil_mode == 0) {
            set_led_color(Green); // Low Mode
        } else {
            set_led_color(Pink);  // High Mode
        }
    } else if(rapid_fire_active) {
        set_led_color(Yellow); // Only Rapid Fire
    } else if(anti_recoil_active) {
        set_led_color(Red);    // Only Anti-Recoil
    }
}

function set_led_color(int color) {
    // Simple LED control
    if(color == Green) {
        set_led(LED_1, FALSE);
        set_led(LED_2, TRUE);
        set_led(LED_3, FALSE);
        set_led(LED_4, FALSE);
    } else if(color == Pink) {
        set_led(LED_1, TRUE);
        set_led(LED_2, FALSE);
        set_led(LED_3, TRUE);
        set_led(LED_4, FALSE);
    } else if(color == Yellow) {
        set_led(LED_1, TRUE);
        set_led(LED_2, TRUE);
        set_led(LED_3, FALSE);
        set_led(LED_4, FALSE);
    } else if(color == Red) {
        set_led(LED_1, TRUE);
        set_led(LED_2, FALSE);
        set_led(LED_3, FALSE);
        set_led(LED_4, FALSE);
    }
}

function handle_rumble_system() {
    if(rumble_count > 0) {
        if(rumble_timer == 0) {
            set_rumble(RUMBLE_A, 100);
        }
        rumble_timer = rumble_timer + get_rtime();
        
        if(rumble_timer >= 250) {
            set_rumble(RUMBLE_A, 0);
        }
        
        if(rumble_timer >= 400) {
            rumble_count--;
            rumble_timer = 0;
        }
    }
}

// ===== COMBOS (Event-driven responses) =====
combo rapid_fire_combo {
    set_val(RT, 100);
    wait(hold_time);
    set_val(RT, 0);
    wait(rest_time);
}

combo aim_assist_combo {
    set_val(RX, get_val(RX) + aim_strength);
    wait(20);
    set_val(RY, get_val(RY) + aim_strength);
    wait(20);
    set_val(RX, get_val(RX) - aim_strength);
    wait(20);
    set_val(RY, get_val(RY) - aim_strength);
    wait(20);
}

combo single_rumble {
    set_rumble(RUMBLE_A, 100);
    wait(200);
    set_rumble(RUMBLE_A, 0);
}
