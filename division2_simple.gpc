/*
 * The Division 2 - Simple & Working Script
 * Version: 1.0 (Based on XBL Jedi Style)
 *
 * FEATURES:
 * - Anti-Recoil (2 Modi)
 * - Rapid Fire
 * - Aim Assist
 * - CutStrafe (<PERSON><PERSON>-<PERSON>)
 * - Einfache Steuerung
 * - LED Feedback
 */

// Button Definitionen (Xbox Style)
define HOME  = 0;
define VIEW  = 1;
define MENU  = 2;
define RB    = 3;
define RT    = 4;
define RS    = 5;
define LB    = 6;
define LT    = 7;
define LS    = 8;
define RX    = 9;
define RY    = 10;
define LX    = 11;
define LY    = 12;
define UP    = 13;
define DOWN  = 14;
define LEFT  = 15;
define RIGHT = 16;
define Y     = 17;
define B     = 18;
define A     = 19;
define X     = 20;

// LED Farben
define Blue    = 1;
define Red     = 2;
define Green   = 3;
define Pink    = 4;
define Yellow  = 6;

// Einstellungen
int anti_recoil_on = TRUE;
int rapid_fire_on = TRUE;
int aim_assist_on = TRUE;
int cutstrafe_on = FALSE;
int anti_recoil_mode = 0; // 0 = Low, 1 = High

// Anti-Recoil Werte
int ar_vertical_low = 25;
int ar_vertical_high = 40;
int ar_horizontal_low = 12;
int ar_horizontal_high = 20;

// Rapid Fire
int fire_rate = 14;
int hold_time = 20;
int rest_time = 15;

// CutStrafe
int cutstrafe_direction = 1;
int cutstrafe_timer = 0;
int cutstrafe_speed = 80;

// Aim Assist
int aim_strength = 20;
int aim_timer = 0;

// Rumble
int rumble_active = 0;
int rumble_timer = 0;

main {
    // STEUERUNG:

    // Anti-Recoil Toggle: LT + D-Pad DOWN
    if(get_val(LT) && event_press(DOWN)) {
        anti_recoil_mode = !anti_recoil_mode;
        rumble_active = anti_recoil_mode + 1; // 1 oder 2 Rumbles
        update_led_color();
    }

    // Rapid Fire Toggle: LT + X
    if(get_val(LT) && event_press(X)) {
        rapid_fire_on = !rapid_fire_on;
        combo_run(toggle_rumble);
        update_led_color();
    }

    // Aim Assist Toggle: LT + Y
    if(get_val(LT) && event_press(Y)) {
        aim_assist_on = !aim_assist_on;
        combo_run(toggle_rumble);
    }

    // CutStrafe Toggle: LT + RIGHT
    if(get_val(LT) && event_press(RIGHT)) {
        cutstrafe_on = !cutstrafe_on;
        combo_run(toggle_rumble);
    }

    // ANTI-RECOIL SYSTEM
    if(anti_recoil_on && get_val(LT) && get_val(RT)) {
        // Vertikaler Anti-Recoil
        if(anti_recoil_mode == 0) {
            set_val(RY, get_val(RY) + ar_vertical_low);
        } else {
            set_val(RY, get_val(RY) + ar_vertical_high);
        }

        // Horizontaler Anti-Recoil (nur wenn wenig Bewegung)
        if(abs(get_val(RX)) < 25) {
            if(anti_recoil_mode == 0) {
                set_val(RX, get_val(RX) + ar_horizontal_low);
            } else {
                set_val(RX, get_val(RX) + ar_horizontal_high);
            }
        }
    }

    // RAPID FIRE SYSTEM
    if(rapid_fire_on && get_val(RT)) {
        combo_run(rapid_fire_combo);
    }

    // AIM ASSIST SYSTEM
    if(aim_assist_on && get_val(LT) && !get_val(RT)) {
        if(aim_timer <= 0) {
            combo_run(aim_assist_combo);
            aim_timer = 40;
        }
    }

    // CUTSTRAFE SYSTEM (Zick-Zack Movement)
    if(cutstrafe_on && get_val(LT)) {
        if(cutstrafe_timer <= 0) {
            if(cutstrafe_direction == 1) {
                set_val(LX, cutstrafe_speed);
                cutstrafe_direction = -1;
            } else {
                set_val(LX, -cutstrafe_speed);
                cutstrafe_direction = 1;
            }
            cutstrafe_timer = 120; // Delay zwischen Bewegungen
        }
    }

    // AUTO SPRINT
    if(abs(get_val(LY)) > 85) {
        set_val(LS, 100);
    }

    // Timer Updates
    if(aim_timer > 0) aim_timer = aim_timer - get_rtime();
    if(cutstrafe_timer > 0) cutstrafe_timer = cutstrafe_timer - get_rtime();

    // Rumble System
    handle_rumble();
}

// FUNKTIONEN
function update_led_color() {
    if(rapid_fire_on && anti_recoil_on) {
        if(anti_recoil_mode == 0) {
            set_led_color(Green); // Low Anti-Recoil + Rapid Fire
        } else {
            set_led_color(Pink);  // High Anti-Recoil + Rapid Fire
        }
    } else if(rapid_fire_on && !anti_recoil_on) {
        set_led_color(Yellow); // Nur Rapid Fire
    } else if(!rapid_fire_on && anti_recoil_on) {
        set_led_color(Blue);   // Nur Anti-Recoil
    } else {
        set_led_color(Red);    // Alles aus
    }
}

function set_led_color(int color) {
    set_led(LED_1, color == Red || color == Pink || color == Yellow);
    set_led(LED_2, color == Green || color == Yellow || color == Blue);
    set_led(LED_3, color == Blue || color == Pink);
    set_led(LED_4, FALSE);
}

function handle_rumble() {
    if(rumble_active > 0) {
        if(rumble_timer == 0) {
            set_rumble(RUMBLE_A, 100);
        }
        rumble_timer = rumble_timer + get_rtime();

        if(rumble_timer >= 250) {
            set_rumble(RUMBLE_A, 0);
        }

        if(rumble_timer >= 400) {
            rumble_active--;
            rumble_timer = 0;
        }
    }
}

// COMBOS
combo rapid_fire_combo {
    set_val(RT, 100);
    wait(hold_time);
    set_val(RT, 0);
    wait(rest_time);
}

combo aim_assist_combo {
    // Kleine kreisförmige Bewegung für Aim Assist
    set_val(RX, get_val(RX) + aim_strength);
    wait(20);
    set_val(RY, get_val(RY) + aim_strength);
    wait(20);
    set_val(RX, get_val(RX) - aim_strength);
    wait(20);
    set_val(RY, get_val(RY) - aim_strength);
    wait(20);
}

combo toggle_rumble {
    set_rumble(RUMBLE_A, 100);
    wait(200);
    set_rumble(RUMBLE_A, 0);
}
