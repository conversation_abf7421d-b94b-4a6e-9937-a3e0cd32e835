# The Division 2 - Komplettes Cronus Zen Script Paket

## 📋 Übersicht
Dieses umfassende Script-Paket bietet alle wichtigen Features für The Division 2, optimiert für ethisches Gaming und verbesserte Spielerfahrung.

## 🎮 Features

### ✅ Anti-Recoil System
- **Vertikaler Rückstoß**: Automatische Kompensation des Waffenrückstoßes
- **Horizontaler Rückstoß**: Intelligente Links/Rechts-Korrektur
- **Waffen-spezifisch**: Verschiedene Profile für AR, SMG, LMG, Marksman

### ✅ Rapid Fire
- **Semi-Auto Optimierung**: Maximiert die Feuerrate bei halbautomatischen Waffen
- **Anpassbare Geschwindigkeit**: Verschiedene Raten je Waffentyp
- **Burst-Fire Modi**: Kontrollierte Salven

### ✅ Aim Assist Verstärkung
- **Sanfte Zielhilfe**: Verstärkt die vorhandene Aim-Assist-Funktion
- **Anpassbare Stärke**: Konfigurierbare Intensität
- **Natürliche Bewegung**: Behält das natürliche Spielgefühl bei

### ✅ Movement Features
- **Auto Sprint**: Automatisches Sprinten bei vollem Stick-Input
- **Drop Shot**: Automatisches Ducken beim Schießen
- **Jump Shot**: Optimierte Sprung-Schuss-Kombination

### ✅ Gameplay Verbesserungen
- **Quick Reload**: Beschleunigtes Nachladen
- **Weapon Swap**: Optimierter Waffenwechsel
- **Profile System**: 4 verschiedene Waffen-Profile

## 🎯 Waffen-Profile

| Profil | Waffentyp | Vertikal | Horizontal | Feuerrate |
|--------|-----------|----------|------------|-----------|
| 1 | Assault Rifle | 35 | 15 | 12 |
| 2 | SMG | 25 | 20 | 15 |
| 3 | LMG | 45 | 25 | 8 |
| 4 | Marksman | 20 | 10 | 6 |

## 🎮 Steuerung

### Feature Aktivierung/Deaktivierung:
- **Anti-Recoil**: `L3 + R3` (gleichzeitig drücken)
- **Rapid Fire**: `D-Pad Oben + Unten` (gleichzeitig)
- **Aim Assist**: `L1 + R1` (gleichzeitig)
- **Auto Sprint**: `Touchpad + Options` (gleichzeitig)

### Profil Wechsel:
- **Waffen-Profil**: `D-Pad Links + Rechts` (gleichzeitig)

### Automatische Features:
- **Drop Shot**: Automatisch beim Schießen + Ducken
- **Jump Shot**: Automatisch beim Schießen + Springen
- **Quick Reload**: Automatisch bei Nachladen

## 🔧 Installation

1. **Cronus Zen Software** öffnen
2. **Script laden**: `division2_complete.gpc` in Cronus Zen importieren
3. **Kompilieren**: Script kompilieren und auf Gerät übertragen
4. **Konfiguration**: Nach Bedarf anpassen

## ⚙️ Konfiguration

### Grundeinstellungen (im Script änderbar):
```gpc
#define ANTI_RECOIL_VERTICAL    35      // Vertikaler Anti-Recoil (0-100)
#define ANTI_RECOIL_HORIZONTAL  15      // Horizontaler Anti-Recoil (0-100)
#define RAPID_FIRE_SPEED        12      // Rapid Fire Geschwindigkeit (1-25)
#define AIM_ASSIST_STRENGTH     25      // Aim Assist Verstärkung (0-100)
#define AUTO_SPRINT_THRESHOLD   85      // Auto Sprint Schwellenwert
```

### Erweiterte Anpassungen:
- **Waffen-Profile**: Arrays `vertical_recoil[]`, `horizontal_recoil[]`, `fire_rate[]`
- **Timing**: Verschiedene Timer-Werte für Combos
- **Feedback**: Rumble-Intensität und -Dauer

## 🎯 Empfohlene Einstellungen

### Für Anfänger:
- Anti-Recoil: 25-30
- Rapid Fire: 10-12
- Aim Assist: 15-20

### Für Fortgeschrittene:
- Anti-Recoil: 35-40
- Rapid Fire: 12-15
- Aim Assist: 20-25

### Für Experten:
- Anti-Recoil: 40-50
- Rapid Fire: 15-20
- Aim Assist: 25-30

## 🔄 Feedback System

Das Script verwendet Rumble-Feedback zur Bestätigung:
- **2x kurz**: Feature aktiviert
- **1x lang**: Feature deaktiviert
- **1x kurz**: Toggle-Aktion

## ⚠️ Wichtige Hinweise

### Ethische Nutzung:
- Dieses Script ist für **ethisches Gaming** konzipiert
- Nutzen Sie es **verantwortungsvoll**
- Respektieren Sie andere Spieler
- Beachten Sie die **Nutzungsbedingungen** des Spiels

### Technische Hinweise:
- **Kompatibilität**: PS4/PS5, Xbox One/Series X|S
- **Firmware**: Cronus Zen Firmware 1.1.0 oder höher
- **Performance**: Optimiert für 60 FPS Gameplay

### Fehlerbehebung:
- **Script lädt nicht**: Firmware aktualisieren
- **Features funktionieren nicht**: Controller-Verbindung prüfen
- **Lag/Verzögerung**: USB-Verbindung optimieren

## 📞 Support

Bei Fragen oder Problemen:
1. **Dokumentation** nochmals durchlesen
2. **Cronus Zen Community** konsultieren
3. **Script-Einstellungen** überprüfen
4. **Hardware-Verbindung** testen

## 📝 Changelog

### Version 2.0:
- ✅ Komplette Überarbeitung
- ✅ 4 Waffen-Profile hinzugefügt
- ✅ Verbesserte Anti-Recoil-Logik
- ✅ Optimierte Rapid Fire
- ✅ Erweiterte Aim Assist
- ✅ Neue Movement Features
- ✅ Feedback System implementiert

---

**Viel Spaß beim Spielen! 🎮**
