/*
 * The Division 2 - Complete Cronus Zen Script
 * Version: 2.1 (Fixed)
 * Author: Cronus Community
 * 
 * Features:
 * - Anti-Recoil (Vertikal & Horizontal)
 * - Rapid Fire für Semi-Auto Waffen
 * - Aim Assist Verstärkung
 * - Auto Sprint
 * - Drop Shot / Jump Shot
 * - Quick Reload
 * - Anpassbare Profile für verschiedene Waffen
 */

// ===== KONFIGURATION =====
define ANTI_RECOIL_VERTICAL    = 35;
define ANTI_RECOIL_HORIZONTAL  = 15;
define RAPID_FIRE_SPEED        = 12;
define AIM_ASSIST_STRENGTH     = 25;
define AUTO_SPRINT_THRESHOLD   = 85;

// ===== VARIABLEN =====
int anti_recoil_active = FALSE;
int rapid_fire_active = FALSE;
int aim_assist_active = FALSE;
int auto_sprint_active = FALSE;
int weapon_profile = 0;
int recoil_timer = 0;
int rapid_fire_timer = 0;

// Waffen-Profile (<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>)
int vertical_recoil[4] = {35, 25, 45, 20};
int horizontal_recoil[4] = {15, 20, 25, 10};
int fire_rate[4] = {12, 15, 8, 6};

// ===== HAUPT SCRIPT =====
main {
    // Profile wechseln mit D-Pad Links + Rechts
    if(event_press(PS4_LEFT) && get_val(PS4_RIGHT)) {
        weapon_profile++;
        if(weapon_profile > 3) weapon_profile = 0;
        combo_run(profile_feedback);
    }
    
    // Anti-Recoil Toggle (L3 + R3)
    if(event_press(PS4_L3) && get_val(PS4_R3)) {
        anti_recoil_active = !anti_recoil_active;
        if(anti_recoil_active) {
            combo_run(feedback_on);
        } else {
            combo_run(feedback_off);
        }
    }
    
    // Rapid Fire Toggle (D-Pad Up + Down)
    if(event_press(PS4_UP) && get_val(PS4_DOWN)) {
        rapid_fire_active = !rapid_fire_active;
        combo_run(feedback_toggle);
    }
    
    // Aim Assist Toggle (L1 + R1)
    if(event_press(PS4_L1) && get_val(PS4_R1)) {
        aim_assist_active = !aim_assist_active;
        combo_run(feedback_toggle);
    }
    
    // Auto Sprint Toggle (Touchpad + Options)
    if(event_press(PS4_TOUCH) && get_val(PS4_OPTIONS)) {
        auto_sprint_active = !auto_sprint_active;
        combo_run(feedback_toggle);
    }
    
    // Anti-Recoil System
    if(get_val(PS4_R2) > 50 && anti_recoil_active) {
        if(recoil_timer <= 0) {
            // Vertikaler Rückstoß
            set_val(PS4_RY, get_val(PS4_RY) + vertical_recoil[weapon_profile]);
            
            // Horizontaler Rückstoß
            if(abs(get_val(PS4_RX)) < 20) {
                set_val(PS4_RX, get_val(PS4_RX) + horizontal_recoil[weapon_profile]);
            }
            recoil_timer = 50;
        }
    }
    
    // Rapid Fire
    if(get_val(PS4_R2) > 50 && rapid_fire_active) {
        if(rapid_fire_timer <= 0) {
            set_val(PS4_R2, 0);
            rapid_fire_timer = fire_rate[weapon_profile];
        }
    }
    
    // Aim Assist Verstärkung
    if(get_val(PS4_L2) > 50 && aim_assist_active) {
        if(abs(get_val(PS4_RX)) > 10 || abs(get_val(PS4_RY)) > 10) {
            set_val(PS4_RX, get_val(PS4_RX) * (100 + AIM_ASSIST_STRENGTH) / 100);
            set_val(PS4_RY, get_val(PS4_RY) * (100 + AIM_ASSIST_STRENGTH) / 100);
        }
    }
    
    // Auto Sprint
    if(auto_sprint_active && abs(get_val(PS4_LY)) > AUTO_SPRINT_THRESHOLD) {
        set_val(PS4_L3, 100);
    }
    
    // Drop Shot
    if(get_val(PS4_R2) > 50 && event_press(PS4_CIRCLE)) {
        combo_run(drop_shot_combo);
    }
    
    // Jump Shot
    if(get_val(PS4_R2) > 50 && event_press(PS4_CROSS)) {
        combo_run(jump_shot_combo);
    }
    
    // Quick Reload
    if(event_press(PS4_SQUARE)) {
        combo_run(quick_reload_combo);
    }
    
    // Timer Updates
    if(recoil_timer > 0) recoil_timer = recoil_timer - get_rtime();
    if(rapid_fire_timer > 0) rapid_fire_timer = rapid_fire_timer - get_rtime();
}

// ===== COMBOS =====

combo drop_shot_combo {
    set_val(PS4_CIRCLE, 100);
    wait(50);
    set_val(PS4_CIRCLE, 0);
    wait(100);
    set_val(PS4_R2, 100);
    wait(200);
    set_val(PS4_R2, 0);
}

combo jump_shot_combo {
    set_val(PS4_CROSS, 100);
    wait(50);
    set_val(PS4_CROSS, 0);
    wait(150);
    set_val(PS4_R2, 100);
    wait(200);
    set_val(PS4_R2, 0);
}

combo quick_reload_combo {
    set_val(PS4_SQUARE, 100);
    wait(50);
    set_val(PS4_SQUARE, 0);
    wait(50);
    set_val(PS4_SQUARE, 100);
    wait(50);
    set_val(PS4_SQUARE, 0);
}

combo feedback_on {
    set_rumble(RUMBLE_A, 100);
    wait(200);
    set_rumble(RUMBLE_A, 0);
    wait(100);
    set_rumble(RUMBLE_A, 100);
    wait(200);
    set_rumble(RUMBLE_A, 0);
}

combo feedback_off {
    set_rumble(RUMBLE_A, 100);
    wait(500);
    set_rumble(RUMBLE_A, 0);
}

combo feedback_toggle {
    set_rumble(RUMBLE_A, 100);
    wait(100);
    set_rumble(RUMBLE_A, 0);
}

combo profile_feedback {
    set_rumble(RUMBLE_A, 50);
    wait(100);
    set_rumble(RUMBLE_A, 0);
}
