/*
 * The Division 2 - OLED Display Script
 * Version: 3.0 (OLED Edition)
 * 
 * FEATURES:
 * - <PERSON>chtes OLED Display Menu
 * - Sichtbare Einstellungen
 * - Anti-Recoil (einstellbar)
 * - Rapid Fire (einstellbar)
 * - Aim Assist (einstellbar)
 * - CutStrafe (einstellbar)
 * - Real-time Werte-Anzeige
 */

// ===== BUTTON DEFINITIONS =====
define HOME = 0; define VIEW = 1; define MENU = 2; define RB = 3;
define RT = 4; define RS = 5; define LB = 6; define LT = 7;
define LS = 8; define RX = 9; define RY = 10; define LX = 11;
define LY = 12; define UP = 13; define DOWN = 14; define LEFT = 15;
define RIGHT = 16; define Y = 17; define B = 18; define A = 19; define X = 20;

// ===== OLED CONSTANTS =====
define FONT_SMALL = 0;
define FONT_MEDIUM = 1;
define FONT_LARGE = 2;
define COLOR_WHITE = 1;
define COLOR_BLACK = 0;

// ===== DATA SECTION (für OLED Text) =====
data(
    "DIVISION 2 SCRIPT",     // 0
    "Anti-Recoil:",          // 18
    "Rapid Fire:",           // 31
    "Aim Assist:",           // 43
    "CutStrafe:",            // 55
    "MENU - LT+OPTIONS",     // 66
    "ON",                    // 83
    "OFF",                   // 87
    "SETTINGS",              // 91
    "Value:",                // 100
    "Step: +/-1",            // 107
    "Step: +/-5",            // 117
    "Step: +/-10",           // 127
    "BACK - B Button",       // 138
    "Vertical:",             // 153
    "Horizontal:",           // 163
    "Fire Rate:",            // 175
    "Strength:",             // 186
    "Speed:",                // 196
    "Timing:"                // 203
);

// ===== VARIABLES =====
// Feature States
int anti_recoil_active = TRUE;
int rapid_fire_active = TRUE;
int aim_assist_active = TRUE;
int cutstrafe_active = FALSE;

// Settings Values
int anti_recoil_vertical = 35;
int anti_recoil_horizontal = 15;
int rapid_fire_rate = 14;
int aim_assist_strength = 25;
int cutstrafe_speed = 80;
int cutstrafe_timing = 120;

// Menu System
int menu_active = FALSE;
int menu_page = 0;        // 0=Main, 1=Anti-Recoil, 2=Rapid Fire, 3=Aim Assist, 4=CutStrafe
int menu_selection = 0;   // Current selection
int adjustment_step = 1;  // 1, 5, or 10

// Timers
int display_timer = 0;
int rapid_fire_timer = 0;
int cutstrafe_timer = 0;
int cutstrafe_direction = 1;

// ===== INIT SECTION =====
init {
    // Clear display and show startup
    cls_oled(COLOR_BLACK);
    printf(10, 10, FONT_MEDIUM, COLOR_WHITE, 0); // "DIVISION 2 SCRIPT"
    printf(20, 30, FONT_SMALL, COLOR_WHITE, 66); // "MENU - LT+OPTIONS"
    display_timer = 2000; // Show for 2 seconds
}

// ===== MAIN SECTION =====
main {
    // Menu Toggle: LT + OPTIONS
    if(get_val(LT) && event_press(MENU)) {
        menu_active = !menu_active;
        if(menu_active) {
            menu_page = 0;
            menu_selection = 0;
            update_display();
        } else {
            show_status_display();
        }
    }
    
    // Menu Navigation
    if(menu_active) {
        handle_menu_navigation();
    } else {
        // Quick Toggles when menu not active
        handle_quick_toggles();
        
        // Apply Features
        apply_features();
        
        // Update status display periodically
        if(display_timer <= 0) {
            show_status_display();
            display_timer = 1000; // Update every second
        }
    }
    
    // Update timers
    if(display_timer > 0) display_timer = display_timer - get_rtime();
    if(rapid_fire_timer > 0) rapid_fire_timer = rapid_fire_timer - get_rtime();
    if(cutstrafe_timer > 0) cutstrafe_timer = cutstrafe_timer - get_rtime();
}

// ===== FUNCTIONS =====

function handle_menu_navigation() {
    // Navigation: UP/DOWN
    if(event_press(UP)) {
        if(menu_page == 0) {
            menu_selection--;
            if(menu_selection < 0) menu_selection = 4;
        } else {
            adjust_value(-adjustment_step);
        }
        update_display();
    }
    
    if(event_press(DOWN)) {
        if(menu_page == 0) {
            menu_selection++;
            if(menu_selection > 4) menu_selection = 0;
        } else {
            adjust_value(adjustment_step);
        }
        update_display();
    }
    
    // Enter Sub-Menu: A Button
    if(event_press(A)) {
        if(menu_page == 0) {
            menu_page = menu_selection + 1;
            update_display();
        }
    }
    
    // Back: B Button
    if(event_press(B)) {
        if(menu_page > 0) {
            menu_page = 0;
            update_display();
        } else {
            menu_active = FALSE;
            show_status_display();
        }
    }
    
    // Adjustment Step: LB/RB
    if(event_press(LB)) {
        adjustment_step = adjustment_step / 5;
        if(adjustment_step < 1) adjustment_step = 10;
        update_display();
    }
    
    if(event_press(RB)) {
        adjustment_step = adjustment_step * 5;
        if(adjustment_step > 10) adjustment_step = 1;
        update_display();
    }
    
    // Toggle Feature: X Button
    if(event_press(X)) {
        toggle_current_feature();
        update_display();
    }
}

function adjust_value(int change) {
    if(menu_page == 1) { // Anti-Recoil
        if(menu_selection == 0) {
            anti_recoil_vertical = anti_recoil_vertical + change;
            if(anti_recoil_vertical < 0) anti_recoil_vertical = 0;
            if(anti_recoil_vertical > 60) anti_recoil_vertical = 60;
        } else {
            anti_recoil_horizontal = anti_recoil_horizontal + change;
            if(anti_recoil_horizontal < 0) anti_recoil_horizontal = 0;
            if(anti_recoil_horizontal > 40) anti_recoil_horizontal = 40;
        }
    } else if(menu_page == 2) { // Rapid Fire
        rapid_fire_rate = rapid_fire_rate + change;
        if(rapid_fire_rate < 4) rapid_fire_rate = 4;
        if(rapid_fire_rate > 25) rapid_fire_rate = 25;
    } else if(menu_page == 3) { // Aim Assist
        aim_assist_strength = aim_assist_strength + change;
        if(aim_assist_strength < 0) aim_assist_strength = 0;
        if(aim_assist_strength > 50) aim_assist_strength = 50;
    } else if(menu_page == 4) { // CutStrafe
        if(menu_selection == 0) {
            cutstrafe_speed = cutstrafe_speed + change;
            if(cutstrafe_speed < 20) cutstrafe_speed = 20;
            if(cutstrafe_speed > 100) cutstrafe_speed = 100;
        } else {
            cutstrafe_timing = cutstrafe_timing + change;
            if(cutstrafe_timing < 50) cutstrafe_timing = 50;
            if(cutstrafe_timing > 300) cutstrafe_timing = 300;
        }
    }
}

function toggle_current_feature() {
    if(menu_page == 1) {
        anti_recoil_active = !anti_recoil_active;
    } else if(menu_page == 2) {
        rapid_fire_active = !rapid_fire_active;
    } else if(menu_page == 3) {
        aim_assist_active = !aim_assist_active;
    } else if(menu_page == 4) {
        cutstrafe_active = !cutstrafe_active;
    }
}

function update_display() {
    cls_oled(COLOR_BLACK);
    
    if(menu_page == 0) {
        // Main Menu
        printf(25, 0, FONT_MEDIUM, COLOR_WHITE, 91); // "SETTINGS"
        
        // Menu Items
        printf(0, 15, FONT_SMALL, COLOR_WHITE, 18); // "Anti-Recoil:"
        if(menu_selection == 0) printf(120, 15, FONT_SMALL, COLOR_WHITE, 0); // Cursor
        
        printf(0, 25, FONT_SMALL, COLOR_WHITE, 31); // "Rapid Fire:"
        if(menu_selection == 1) printf(120, 25, FONT_SMALL, COLOR_WHITE, 0);
        
        printf(0, 35, FONT_SMALL, COLOR_WHITE, 43); // "Aim Assist:"
        if(menu_selection == 2) printf(120, 35, FONT_SMALL, COLOR_WHITE, 0);
        
        printf(0, 45, FONT_SMALL, COLOR_WHITE, 55); // "CutStrafe:"
        if(menu_selection == 3) printf(120, 45, FONT_SMALL, COLOR_WHITE, 0);
        
        printf(0, 55, FONT_SMALL, COLOR_WHITE, 138); // "BACK - B Button"
        if(menu_selection == 4) printf(120, 55, FONT_SMALL, COLOR_WHITE, 0);
        
    } else if(menu_page == 1) {
        // Anti-Recoil Settings
        printf(15, 0, FONT_MEDIUM, COLOR_WHITE, 18); // "Anti-Recoil:"
        
        printf(0, 20, FONT_SMALL, COLOR_WHITE, 153); // "Vertical:"
        printf(70, 20, FONT_SMALL, COLOR_WHITE, anti_recoil_vertical);
        
        printf(0, 30, FONT_SMALL, COLOR_WHITE, 163); // "Horizontal:"
        printf(70, 30, FONT_SMALL, COLOR_WHITE, anti_recoil_horizontal);
        
        // Status
        if(anti_recoil_active) {
            printf(0, 45, FONT_SMALL, COLOR_WHITE, 83); // "ON"
        } else {
            printf(0, 45, FONT_SMALL, COLOR_WHITE, 87); // "OFF"
        }
        
        // Step indicator
        if(adjustment_step == 1) printf(0, 55, FONT_SMALL, COLOR_WHITE, 107);
        else if(adjustment_step == 5) printf(0, 55, FONT_SMALL, COLOR_WHITE, 117);
        else printf(0, 55, FONT_SMALL, COLOR_WHITE, 127);
        
    } else if(menu_page == 2) {
        // Rapid Fire Settings
        printf(15, 0, FONT_MEDIUM, COLOR_WHITE, 31); // "Rapid Fire:"
        
        printf(0, 20, FONT_SMALL, COLOR_WHITE, 175); // "Fire Rate:"
        printf(70, 20, FONT_SMALL, COLOR_WHITE, rapid_fire_rate);
        
        if(rapid_fire_active) {
            printf(0, 35, FONT_SMALL, COLOR_WHITE, 83); // "ON"
        } else {
            printf(0, 35, FONT_SMALL, COLOR_WHITE, 87); // "OFF"
        }
        
        if(adjustment_step == 1) printf(0, 50, FONT_SMALL, COLOR_WHITE, 107);
        else if(adjustment_step == 5) printf(0, 50, FONT_SMALL, COLOR_WHITE, 117);
        else printf(0, 50, FONT_SMALL, COLOR_WHITE, 127);
        
    } else if(menu_page == 3) {
        // Aim Assist Settings
        printf(15, 0, FONT_MEDIUM, COLOR_WHITE, 43); // "Aim Assist:"
        
        printf(0, 20, FONT_SMALL, COLOR_WHITE, 186); // "Strength:"
        printf(70, 20, FONT_SMALL, COLOR_WHITE, aim_assist_strength);
        
        if(aim_assist_active) {
            printf(0, 35, FONT_SMALL, COLOR_WHITE, 83); // "ON"
        } else {
            printf(0, 35, FONT_SMALL, COLOR_WHITE, 87); // "OFF"
        }
        
        if(adjustment_step == 1) printf(0, 50, FONT_SMALL, COLOR_WHITE, 107);
        else if(adjustment_step == 5) printf(0, 50, FONT_SMALL, COLOR_WHITE, 117);
        else printf(0, 50, FONT_SMALL, COLOR_WHITE, 127);
        
    } else if(menu_page == 4) {
        // CutStrafe Settings
        printf(20, 0, FONT_MEDIUM, COLOR_WHITE, 55); // "CutStrafe:"
        
        printf(0, 20, FONT_SMALL, COLOR_WHITE, 196); // "Speed:"
        printf(50, 20, FONT_SMALL, COLOR_WHITE, cutstrafe_speed);
        
        printf(0, 30, FONT_SMALL, COLOR_WHITE, 203); // "Timing:"
        printf(50, 30, FONT_SMALL, COLOR_WHITE, cutstrafe_timing);
        
        if(cutstrafe_active) {
            printf(0, 45, FONT_SMALL, COLOR_WHITE, 83); // "ON"
        } else {
            printf(0, 45, FONT_SMALL, COLOR_WHITE, 87); // "OFF"
        }
        
        if(adjustment_step == 1) printf(0, 55, FONT_SMALL, COLOR_WHITE, 107);
        else if(adjustment_step == 5) printf(0, 55, FONT_SMALL, COLOR_WHITE, 117);
        else printf(0, 55, FONT_SMALL, COLOR_WHITE, 127);
    }
}

function show_status_display() {
    cls_oled(COLOR_BLACK);
    
    // Title
    printf(10, 0, FONT_MEDIUM, COLOR_WHITE, 0); // "DIVISION 2 SCRIPT"
    
    // Status Line 1
    printf(0, 20, FONT_SMALL, COLOR_WHITE, 18); // "Anti-Recoil:"
    if(anti_recoil_active) {
        printf(80, 20, FONT_SMALL, COLOR_WHITE, 83); // "ON"
    } else {
        printf(80, 20, FONT_SMALL, COLOR_WHITE, 87); // "OFF"
    }
    
    // Status Line 2
    printf(0, 30, FONT_SMALL, COLOR_WHITE, 31); // "Rapid Fire:"
    if(rapid_fire_active) {
        printf(80, 30, FONT_SMALL, COLOR_WHITE, 83); // "ON"
    } else {
        printf(80, 30, FONT_SMALL, COLOR_WHITE, 87); // "OFF"
    }
    
    // Status Line 3
    printf(0, 40, FONT_SMALL, COLOR_WHITE, 43); // "Aim Assist:"
    if(aim_assist_active) {
        printf(80, 40, FONT_SMALL, COLOR_WHITE, 83); // "ON"
    } else {
        printf(80, 40, FONT_SMALL, COLOR_WHITE, 87); // "OFF"
    }
    
    // Status Line 4
    printf(0, 50, FONT_SMALL, COLOR_WHITE, 55); // "CutStrafe:"
    if(cutstrafe_active) {
        printf(80, 50, FONT_SMALL, COLOR_WHITE, 83); // "ON"
    } else {
        printf(80, 50, FONT_SMALL, COLOR_WHITE, 87); // "OFF"
    }
}

function handle_quick_toggles() {
    // Quick Toggles (LT + Button)
    if(get_val(LT) && event_press(DOWN)) {
        anti_recoil_active = !anti_recoil_active;
        show_status_display();
    }
    
    if(get_val(LT) && event_press(X)) {
        rapid_fire_active = !rapid_fire_active;
        show_status_display();
    }
    
    if(get_val(LT) && event_press(Y)) {
        aim_assist_active = !aim_assist_active;
        show_status_display();
    }
    
    if(get_val(LT) && event_press(RIGHT)) {
        cutstrafe_active = !cutstrafe_active;
        show_status_display();
    }
}

function apply_features() {
    // Anti-Recoil
    if(anti_recoil_active && get_val(LT) && get_val(RT)) {
        set_val(RY, get_val(RY) + anti_recoil_vertical);
        if(abs(get_val(RX)) < 25) {
            set_val(RX, get_val(RX) + anti_recoil_horizontal);
        }
    }
    
    // Rapid Fire
    if(rapid_fire_active && get_val(RT)) {
        if(rapid_fire_timer <= 0) {
            combo_run(rapid_fire_combo);
            rapid_fire_timer = rapid_fire_rate * 10;
        }
    }
    
    // Aim Assist
    if(aim_assist_active && get_val(LT) && !get_val(RT)) {
        if(abs(get_val(RX)) > 10 || abs(get_val(RY)) > 10) {
            set_val(RX, get_val(RX) * (100 + aim_assist_strength) / 100);
            set_val(RY, get_val(RY) * (100 + aim_assist_strength) / 100);
        }
    }
    
    // CutStrafe
    if(cutstrafe_active && get_val(LT)) {
        if(cutstrafe_timer <= 0) {
            if(cutstrafe_direction == 1) {
                set_val(LX, cutstrafe_speed);
                cutstrafe_direction = -1;
            } else {
                set_val(LX, -cutstrafe_speed);
                cutstrafe_direction = 1;
            }
            cutstrafe_timer = cutstrafe_timing;
        }
    }
    
    // Auto Sprint
    if(abs(get_val(LY)) > 85) {
        set_val(LS, 100);
    }
}

// ===== COMBOS =====
combo rapid_fire_combo {
    set_val(RT, 100);
    wait(20);
    set_val(RT, 0);
    wait(rapid_fire_rate);
}
