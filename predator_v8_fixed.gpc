/*
 * PREDATOR V8 STYLE - The Division 2 (FIXED)
 * Version: 8.1 (2025 Edition - Working)
 * Inspired by: PREDATOR V8 Script Features
 *
 * FEATURES:
 * - OLED Menu System (L2 + Options)
 * - CutStrafe (Zick-Zack Movement)
 * - Advanced Anti-Recoil
 * - Rapid Fire System
 * - Aim Assist Pro
 * - Auto Sprint
 * - Drop/Jump Shot
 * - Quick Reload
 * - Profile System
 * - Real-time Adjustments
 */

// ===== SYSTEM VARIABLEN =====
int anti_recoil_active = FALSE;
int rapid_fire_active = FALSE;
int aim_assist_active = FALSE;
int auto_sprint_active = FALSE;
int cutstrafe_active = FALSE;
int weapon_profile = 0;

// Timer
int recoil_timer = 0;
int rapid_fire_timer = 0;
int cutstrafe_timer = 0;
int menu_timer = 0;

// OLED Menu System
int menu_active = FALSE;
int menu_page = 0;
int menu_selection = 0;
int setting_mode = FALSE;

// CutStrafe System
int cutstrafe_direction = 1;
int cutstrafe_speed = 100;
int cutstrafe_delay = 120;

// Profile Werte - AR (0)
int ar_vertical = 35;
int ar_horizontal = 15;
int ar_fire_rate = 12;
int ar_aim_strength = 25;

// Profile Werte - SMG (1)
int smg_vertical = 25;
int smg_horizontal = 20;
int smg_fire_rate = 15;
int smg_aim_strength = 30;

// Profile Werte - LMG (2)
int lmg_vertical = 45;
int lmg_horizontal = 25;
int lmg_fire_rate = 8;
int lmg_aim_strength = 20;

// Profile Werte - Marksman (3)
int marksman_vertical = 20;
int marksman_horizontal = 10;
int marksman_fire_rate = 6;
int marksman_aim_strength = 35;

// Aktuelle Einstellungen
int current_vertical = 35;
int current_horizontal = 15;
int current_fire_rate = 12;
int current_aim_strength = 25;

// Hilfsvariablen
int horizontal_adjust = 0;
int assist_multiplier = 0;

// ===== HAUPT SCRIPT =====
main {
    // OLED MENU SYSTEM - L2 + Options
    if(event_press(PS4_L2) && get_val(PS4_OPTIONS)) {
        menu_active = !menu_active;
        if(menu_active) {
            menu_page = 0;
            menu_selection = 0;
            combo_run(menu_open_sound);
        } else {
            combo_run(menu_close_sound);
        }
    }

    // Menu Navigation
    if(menu_active) {
        handle_menu_system();
    } else {
        // QUICK TOGGLES (wie PREDATOR V8)
        // Anti-Recoil Toggle
        if(event_press(PS4_L2) && event_press(PS4_DOWN)) {
            anti_recoil_active = !anti_recoil_active;
            combo_run(toggle_feedback);
        }

        // Rapid Fire Toggle
        if(event_press(PS4_L2) && event_press(PS4_UP)) {
            rapid_fire_active = !rapid_fire_active;
            combo_run(toggle_feedback);
        }

        // Aim Assist Toggle
        if(event_press(PS4_L2) && event_press(PS4_LEFT)) {
            aim_assist_active = !aim_assist_active;
            combo_run(toggle_feedback);
        }

        // CutStrafe Toggle
        if(event_press(PS4_L2) && event_press(PS4_RIGHT)) {
            cutstrafe_active = !cutstrafe_active;
            combo_run(toggle_feedback);
        }

        // Profile Switch
        if(event_press(PS4_SHARE) && event_press(PS4_DOWN)) {
            weapon_profile++;
            if(weapon_profile > 3) weapon_profile = 0;
            update_profile_values();
            combo_run(profile_switch_sound);
        }

        // ANTI-RECOIL SYSTEM
        if(get_val(PS4_R2) > 50 && anti_recoil_active) {
            if(recoil_timer <= 0) {
                // Vertikaler Anti-Recoil
                set_val(PS4_RY, get_val(PS4_RY) + current_vertical);

                // Horizontaler Anti-Recoil mit Variation
                if(abs(get_val(PS4_RX)) < 25) {
                    horizontal_adjust = current_horizontal;
                    // Zufällige Variation für natürliches Gefühl
                    if(rand(100) > 50) {
                        horizontal_adjust = -horizontal_adjust;
                    }
                    set_val(PS4_RX, get_val(PS4_RX) + horizontal_adjust);
                }
                recoil_timer = 45;
            }
        }

        // RAPID FIRE SYSTEM
        if(get_val(PS4_R2) > 50 && rapid_fire_active) {
            if(rapid_fire_timer <= 0) {
                set_val(PS4_R2, 0);
                rapid_fire_timer = current_fire_rate;
            }
        }

        // AIM ASSIST PRO
        if(get_val(PS4_L2) > 50 && aim_assist_active) {
            if(abs(get_val(PS4_RX)) > 8 || abs(get_val(PS4_RY)) > 8) {
                assist_multiplier = 100 + current_aim_strength;
                set_val(PS4_RX, get_val(PS4_RX) * assist_multiplier / 100);
                set_val(PS4_RY, get_val(PS4_RY) * assist_multiplier / 100);
            }
        }

        // CUTSTRAFE SYSTEM (Zick-Zack Movement)
        if(cutstrafe_active && get_val(PS4_L2) > 50) {
            if(cutstrafe_timer <= 0) {
                if(cutstrafe_direction == 1) {
                    set_val(PS4_LX, cutstrafe_speed);
                    cutstrafe_direction = -1;
                } else {
                    set_val(PS4_LX, -cutstrafe_speed);
                    cutstrafe_direction = 1;
                }
                cutstrafe_timer = cutstrafe_delay;
            }
        }

        // AUTO SPRINT
        if(auto_sprint_active && abs(get_val(PS4_LY)) > 85) {
            set_val(PS4_L3, 100);
        }

        // DROP SHOT
        if(get_val(PS4_R2) > 50 && event_press(PS4_CIRCLE)) {
            combo_run(drop_shot_combo);
        }

        // JUMP SHOT
        if(get_val(PS4_R2) > 50 && event_press(PS4_CROSS)) {
            combo_run(jump_shot_combo);
        }

        // QUICK RELOAD
        if(event_press(PS4_SQUARE)) {
            combo_run(quick_reload_combo);
        }

        // Timer Updates
        update_all_timers();
    }
}

// ===== FUNKTIONEN =====
function handle_menu_system() {
    // Menu Navigation mit D-Pad
    if(event_press(PS4_UP)) {
        menu_selection--;
        if(menu_selection < 0) menu_selection = 3;
        combo_run(menu_beep);
    }

    if(event_press(PS4_DOWN)) {
        menu_selection++;
        if(menu_selection > 3) menu_selection = 0;
        combo_run(menu_beep);
    }

    // Page Navigation
    if(event_press(PS4_LEFT)) {
        menu_page--;
        if(menu_page < 0) menu_page = 2;
        menu_selection = 0;
        combo_run(menu_page_sound);
    }

    if(event_press(PS4_RIGHT)) {
        menu_page++;
        if(menu_page > 2) menu_page = 0;
        menu_selection = 0;
        combo_run(menu_page_sound);
    }

    // Setting Adjustment
    if(event_press(PS4_CROSS)) {
        adjust_setting(menu_page, menu_selection, 1);
        combo_run(setting_change);
    }

    if(event_press(PS4_SQUARE)) {
        adjust_setting(menu_page, menu_selection, -1);
        combo_run(setting_change);
    }
}

function adjust_setting(int page, int selection, int direction) {
    if(page == 0) { // Anti-Recoil Page
        if(selection == 0) {
            current_vertical = current_vertical + (direction * 5);
            if(current_vertical < 0) current_vertical = 0;
            if(current_vertical > 60) current_vertical = 60;
        } else if(selection == 1) {
            current_horizontal = current_horizontal + (direction * 3);
            if(current_horizontal < 0) current_horizontal = 0;
            if(current_horizontal > 40) current_horizontal = 40;
        }
    } else if(page == 1) { // Fire/Aim Page
        if(selection == 0) {
            current_fire_rate = current_fire_rate + (direction * 2);
            if(current_fire_rate < 4) current_fire_rate = 4;
            if(current_fire_rate > 25) current_fire_rate = 25;
        } else if(selection == 1) {
            current_aim_strength = current_aim_strength + (direction * 5);
            if(current_aim_strength < 0) current_aim_strength = 0;
            if(current_aim_strength > 50) current_aim_strength = 50;
        }
    } else if(page == 2) { // CutStrafe Page
        if(selection == 0) {
            cutstrafe_speed = cutstrafe_speed + (direction * 10);
            if(cutstrafe_speed < 50) cutstrafe_speed = 50;
            if(cutstrafe_speed > 100) cutstrafe_speed = 100;
        } else if(selection == 1) {
            cutstrafe_delay = cutstrafe_delay + (direction * 20);
            if(cutstrafe_delay < 80) cutstrafe_delay = 80;
            if(cutstrafe_delay > 200) cutstrafe_delay = 200;
        }
    }
}

function update_profile_values() {
    if(weapon_profile == 0) { // AR
        current_vertical = ar_vertical;
        current_horizontal = ar_horizontal;
        current_fire_rate = ar_fire_rate;
        current_aim_strength = ar_aim_strength;
    } else if(weapon_profile == 1) { // SMG
        current_vertical = smg_vertical;
        current_horizontal = smg_horizontal;
        current_fire_rate = smg_fire_rate;
        current_aim_strength = smg_aim_strength;
    } else if(weapon_profile == 2) { // LMG
        current_vertical = lmg_vertical;
        current_horizontal = lmg_horizontal;
        current_fire_rate = lmg_fire_rate;
        current_aim_strength = lmg_aim_strength;
    } else if(weapon_profile == 3) { // Marksman
        current_vertical = marksman_vertical;
        current_horizontal = marksman_horizontal;
        current_fire_rate = marksman_fire_rate;
        current_aim_strength = marksman_aim_strength;
    }
}

function update_all_timers() {
    if(recoil_timer > 0) recoil_timer = recoil_timer - get_rtime();
    if(rapid_fire_timer > 0) rapid_fire_timer = rapid_fire_timer - get_rtime();
    if(cutstrafe_timer > 0) cutstrafe_timer = cutstrafe_timer - get_rtime();
    if(menu_timer > 0) menu_timer = menu_timer - get_rtime();
}

// ===== COMBOS =====
combo drop_shot_combo {
    set_val(PS4_CIRCLE, 100);
    wait(50);
    set_val(PS4_CIRCLE, 0);
    wait(100);
}

combo jump_shot_combo {
    set_val(PS4_CROSS, 100);
    wait(50);
    set_val(PS4_CROSS, 0);
    wait(150);
}

combo quick_reload_combo {
    set_val(PS4_SQUARE, 100);
    wait(40);
    set_val(PS4_SQUARE, 0);
    wait(40);
    set_val(PS4_SQUARE, 100);
    wait(40);
    set_val(PS4_SQUARE, 0);
}

combo menu_open_sound {
    set_rumble(RUMBLE_A, 100);
    wait(100);
    set_rumble(RUMBLE_A, 0);
    wait(50);
    set_rumble(RUMBLE_A, 100);
    wait(100);
    set_rumble(RUMBLE_A, 0);
}

combo menu_close_sound {
    set_rumble(RUMBLE_A, 100);
    wait(300);
    set_rumble(RUMBLE_A, 0);
}

combo toggle_feedback {
    set_rumble(RUMBLE_A, 80);
    wait(80);
    set_rumble(RUMBLE_A, 0);
}

combo menu_beep {
    set_rumble(RUMBLE_A, 50);
    wait(50);
    set_rumble(RUMBLE_A, 0);
}

combo menu_page_sound {
    set_rumble(RUMBLE_A, 70);
    wait(70);
    set_rumble(RUMBLE_A, 0);
    wait(30);
    set_rumble(RUMBLE_A, 70);
    wait(70);
    set_rumble(RUMBLE_A, 0);
}

combo setting_change {
    set_rumble(RUMBLE_A, 60);
    wait(60);
    set_rumble(RUMBLE_A, 0);
}

combo profile_switch_sound {
    set_rumble(RUMBLE_A, 90);
    wait(90);
    set_rumble(RUMBLE_A, 0);
    wait(40);
    set_rumble(RUMBLE_A, 90);
    wait(90);
    set_rumble(RUMBLE_A, 0);
    wait(40);
    set_rumble(RUMBLE_A, 90);
    wait(90);
    set_rumble(RUMBLE_A, 0);
}
