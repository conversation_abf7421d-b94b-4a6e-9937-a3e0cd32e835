/*
 * The Division 2 - Erweiterte Waffen-Profile
 * Version: 1.0
 * 
 * Spezifische Einstellungen für verschiedene Waffen in The Division 2
 * Optimiert für maximale Effektivität und natürliches Spielgefühl
 */

// ===== ASSAULT RIFLES =====
// Optimiert für: AK-M, FAMAS, G36, M4A1, etc.
int ar_vertical_recoil = 35;
int ar_horizontal_recoil = 15;
int ar_fire_rate = 12;
int ar_aim_assist = 25;

// ===== SUBMACHINE GUNS =====
// Optimiert für: Vector, MP5, UMP-45, etc.
int smg_vertical_recoil = 25;
int smg_horizontal_recoil = 20;
int smg_fire_rate = 15;
int smg_aim_assist = 30;

// ===== LIGHT MACHINE GUNS =====
// Optimiert für: M60, MG5, L86 LSW, etc.
int lmg_vertical_recoil = 45;
int lmg_horizontal_recoil = 25;
int lmg_fire_rate = 8;
int lmg_aim_assist = 20;

// ===== MARKSMAN RIFLES =====
// Optimiert für: M1A, SVD, SRS, etc.
int marksman_vertical_recoil = 20;
int marksman_horizontal_recoil = 10;
int marksman_fire_rate = 6;
int marksman_aim_assist = 35;

// ===== SHOTGUNS =====
// Optimiert für: M870, Super 90, SASG-12, etc.
int shotgun_vertical_recoil = 50;
int shotgun_horizontal_recoil = 30;
int shotgun_fire_rate = 4;
int shotgun_aim_assist = 40;

// ===== PISTOLS =====
// Optimiert für: M1911, P416, etc.
int pistol_vertical_recoil = 15;
int pistol_horizontal_recoil = 8;
int pistol_fire_rate = 18;
int pistol_aim_assist = 45;

// ===== SPEZIELLE WAFFEN-EINSTELLUNGEN =====

// Exotische Waffen
int exotic_vertical_recoil = 30;
int exotic_horizontal_recoil = 18;
int exotic_fire_rate = 10;
int exotic_aim_assist = 28;

// High-End Waffen
int highend_vertical_recoil = 32;
int highend_horizontal_recoil = 16;
int highend_fire_rate = 11;
int highend_aim_assist = 26;

// ===== SITUATIVE PROFILE =====

// PvP Optimiert
int pvp_vertical_recoil = 40;
int pvp_horizontal_recoil = 22;
int pvp_fire_rate = 14;
int pvp_aim_assist = 35;

// PvE Optimiert
int pve_vertical_recoil = 30;
int pve_horizontal_recoil = 12;
int pve_fire_rate = 10;
int pve_aim_assist = 20;

// Raid Optimiert
int raid_vertical_recoil = 35;
int raid_horizontal_recoil = 15;
int raid_fire_rate = 8;
int raid_aim_assist = 22;

// ===== ERWEITERTE KONFIGURATION =====

// Distanz-basierte Anpassungen
int close_range_modifier = 120;    // 0-15m
int mid_range_modifier = 100;      // 15-40m
int long_range_modifier = 80;      // 40m+

// Bewegungs-basierte Anpassungen
int standing_modifier = 100;
int crouching_modifier = 85;
int moving_modifier = 115;

// Gesundheits-basierte Anpassungen
int full_health_modifier = 100;
int low_health_modifier = 130;     // Erhöhte Hilfe bei niedriger Gesundheit

// ===== WAFFEN-SPEZIFISCHE FUNKTIONEN =====

// Burst Fire für bestimmte Waffen
function apply_burst_fire(int burst_count, int burst_delay) {
    int i;
    for(i = 0; i < burst_count; i++) {
        set_val(PS4_R2, 100);
        wait(50);
        set_val(PS4_R2, 0);
        wait(burst_delay);
    }
}

// Adaptive Recoil basierend auf Feuerrate
function adaptive_recoil_control(int base_recoil, int shots_fired) {
    int adaptive_recoil = base_recoil;
    
    // Erhöhe Rückstoß mit anhaltender Feuerrate
    if(shots_fired > 10) {
        adaptive_recoil = base_recoil * 110 / 100;
    }
    if(shots_fired > 20) {
        adaptive_recoil = base_recoil * 120 / 100;
    }
    if(shots_fired > 30) {
        adaptive_recoil = base_recoil * 130 / 100;
    }
    
    return adaptive_recoil;
}

// Intelligente Aim Assist basierend auf Zielentfernung
function smart_aim_assist(int base_assist, int target_distance) {
    int smart_assist = base_assist;
    
    // Nähere Ziele = weniger Assist
    if(target_distance < 15) {
        smart_assist = base_assist * 80 / 100;
    }
    // Mittlere Distanz = normaler Assist
    else if(target_distance < 40) {
        smart_assist = base_assist;
    }
    // Weite Distanz = mehr Assist
    else {
        smart_assist = base_assist * 120 / 100;
    }
    
    return smart_assist;
}

// ===== WAFFEN-ERKENNUNG =====

// Automatische Waffen-Profil Erkennung basierend auf Feuerrate
function detect_weapon_type() {
    static int last_shot_time = 0;
    static int shot_interval = 0;
    static int weapon_type = 1;
    
    if(event_press(PS4_R2)) {
        shot_interval = get_rtime() - last_shot_time;
        last_shot_time = get_rtime();
        
        // Bestimme Waffentyp basierend auf Schussintervall
        if(shot_interval < 100) {
            weapon_type = 2; // SMG
        } else if(shot_interval < 150) {
            weapon_type = 1; // AR
        } else if(shot_interval < 300) {
            weapon_type = 4; // Marksman
        } else {
            weapon_type = 3; // LMG
        }
    }
    
    return weapon_type;
}

// ===== PERFORMANCE OPTIMIERUNGEN =====

// Reduzierte CPU-Last durch intelligente Updates
int performance_counter = 0;
int update_frequency = 5; // Update alle 5 Frames

function optimized_recoil_update() {
    performance_counter++;
    
    if(performance_counter >= update_frequency) {
        // Führe Recoil-Berechnungen durch
        performance_counter = 0;
        return TRUE;
    }
    
    return FALSE;
}

// ===== ANTI-CHEAT SCHUTZ =====

// Natürliche Variationen um Erkennung zu vermeiden
function add_natural_variation(int base_value) {
    int variation = rand(10) - 5; // -5 bis +5 Variation
    return base_value + variation;
}

// Zufällige Delays um menschliches Verhalten zu simulieren
function human_like_delay() {
    int delay = 45 + rand(15); // 45-60ms Delay
    wait(delay);
}
