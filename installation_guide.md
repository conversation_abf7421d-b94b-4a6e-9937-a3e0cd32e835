# 🚀 Installation & Setup Guide - The Division 2 Cronus Zen Script

## 📋 Voraussetzungen

### Hardware:
- ✅ **Cronus Zen Gerät** (Firmware 1.1.0 oder höher)
- ✅ **PlayStation 4/5** oder **Xbox One/Series X|S**
- ✅ **USB-Kabel** (für Cronus Zen Verbindung)
- ✅ **Controller** (Original oder kompatibel)
- ✅ **PC/Laptop** (für Setup)

### Software:
- ✅ **Cronus Zen Software** (neueste Version)
- ✅ **The Division 2** (installiert und aktualisiert)
- ✅ **USB-Treiber** (automatisch installiert)

## 🔧 Schritt-für-Schritt Installation

### Schritt 1: Cronus Zen Software Setup
1. **Download**: Laden Sie die neueste Cronus Zen Software von der offiziellen Website herunter
2. **Installation**: Führen Sie das Setup als Administrator aus
3. **Treiber**: Lassen Sie alle USB-Treiber automatisch installieren
4. **Neustart**: Starten Sie den PC nach der Installation neu

### Schritt 2: Hardware-Verbindung
```
PC ←→ Cronus Zen ←→ Konsole
     USB-A/C      PROG/OUT Port
```

1. **Cronus Zen** mit PC verbinden (USB-A zu USB-C Kabel)
2. **Controller** an Cronus Zen anschließen (CONTROLLER Port)
3. **Konsole** mit Cronus Zen verbinden (CONSOLE Port)
4. **Power LED** sollte blau leuchten

### Schritt 3: Script Installation

#### Option A: Haupt-Script (Empfohlen)
1. **Cronus Zen Software** öffnen
2. **"Programmer"** Tab auswählen
3. **"Open"** klicken → `division2_complete.gpc` auswählen
4. **"Compile"** klicken (grüner Pfeil)
5. **"Program Device"** klicken (roter Pfeil)

#### Option B: Erweiterte Installation
1. **Alle Scripts** in einen Ordner kopieren
2. **Haupt-Script** (`division2_complete.gpc`) öffnen
3. **Include-Zeilen** hinzufügen:
   ```gpc
   #include "weapon_profiles.gpc"
   #include "advanced_features.gpc"
   ```
4. **Kompilieren** und **übertragen**

### Schritt 4: Konsolen-Setup

#### PlayStation 4/5:
1. **Controller** als Player 1 verbinden
2. **Cronus Zen** wird automatisch erkannt
3. **The Division 2** starten
4. **Script** ist sofort aktiv

#### Xbox One/Series X|S:
1. **Controller** einschalten und verbinden
2. **Xbox Button** 10 Sekunden gedrückt halten (falls nötig)
3. **Cronus Zen** sollte grün blinken
4. **The Division 2** starten

## ⚙️ Erste Konfiguration

### 1. Script-Test:
- **L3 + R3** drücken → Rumble = Anti-Recoil aktiviert
- **D-Pad Oben + Unten** → Rumble = Rapid Fire aktiviert
- **L1 + R1** → Rumble = Aim Assist aktiviert

### 2. Waffen-Profil Test:
- **D-Pad Links + Rechts** → Profil wechseln
- **Verschiedene Waffen** testen
- **Einstellungen** nach Bedarf anpassen

### 3. Feintuning:
```gpc
// In division2_complete.gpc anpassen:
#define ANTI_RECOIL_VERTICAL    35  // Ihre Präferenz (20-50)
#define RAPID_FIRE_SPEED        12  // Ihre Präferenz (8-18)
#define AIM_ASSIST_STRENGTH     25  // Ihre Präferenz (15-35)
```

## 🎮 Konsolen-spezifische Einstellungen

### PlayStation Einstellungen:
1. **Einstellungen** → **Geräte** → **Controller**
2. **Vibration** aktivieren
3. **Kommunikationsmethode** → **USB-Kabel verwenden**
4. **Controller-Lautsprecher** → **Aus** (optional)

### Xbox Einstellungen:
1. **Einstellungen** → **Geräte & Verbindungen**
2. **Controller** → **Vibration** aktivieren
3. **USB-Verbindung** bevorzugen
4. **Controller-Updates** prüfen

### The Division 2 Einstellungen:
```
Steuerung:
- Look Sensitivity: 15-25 (je nach Präferenz)
- ADS Sensitivity: 10-20
- Deadzone: 5-10
- Response Curve: Linear oder Dynamic

Audio:
- Master Volume: 80%
- SFX Volume: 100% (für Audio-Cues)
- Voice Chat: Nach Bedarf
```

## 🔍 Fehlerbehebung

### Problem: Script lädt nicht
**Lösung:**
1. **Firmware** aktualisieren
2. **USB-Kabel** wechseln
3. **Antivirus** temporär deaktivieren
4. **Als Administrator** ausführen

### Problem: Features funktionieren nicht
**Lösung:**
1. **Controller-Verbindung** prüfen
2. **Script** neu kompilieren
3. **Konsolen-Einstellungen** überprüfen
4. **Cronus Zen** neu starten

### Problem: Lag/Verzögerung
**Lösung:**
1. **USB 3.0 Port** verwenden
2. **Andere USB-Geräte** entfernen
3. **Polling Rate** reduzieren
4. **Performance Mode** aktivieren

### Problem: Erkennung durch Spiel
**Lösung:**
1. **Natürliche Variationen** aktivieren
2. **Einstellungen** reduzieren
3. **Pausen** einlegen
4. **Script** temporär deaktivieren

## 📊 Performance Optimierung

### Für beste Performance:
```gpc
// Performance-Einstellungen
#define PERFORMANCE_MODE        TRUE
#define UPDATE_FREQUENCY        5      // Alle 5 Frames
#define NATURAL_VARIATION       TRUE   // Anti-Detection
#define ADAPTIVE_LEARNING       FALSE  // CPU-schonend
```

### Für maximale Features:
```gpc
// Feature-Einstellungen
#define PERFORMANCE_MODE        FALSE
#define UPDATE_FREQUENCY        1      // Jeder Frame
#define NATURAL_VARIATION       TRUE
#define ADAPTIVE_LEARNING       TRUE   // KI-Features
```

## 🛡️ Sicherheits-Tipps

### Ethische Nutzung:
- ✅ **Nur in PvE** verwenden (empfohlen)
- ✅ **Moderate Einstellungen** wählen
- ✅ **Pausen** einlegen
- ✅ **Respekt** gegenüber anderen Spielern

### Technische Sicherheit:
- ✅ **Regelmäßige Updates** installieren
- ✅ **Backup** der Einstellungen erstellen
- ✅ **Original Scripts** verwenden
- ✅ **Verdächtige Downloads** vermeiden

## 📞 Support & Community

### Bei Problemen:
1. **README.md** durchlesen
2. **Installation Guide** befolgen
3. **Cronus Community** fragen
4. **Support kontaktieren**

### Nützliche Links:
- **Cronus Zen Support**: [support.cronus.support](https://support.cronus.support)
- **Community Forum**: [community.cronusmax.com](https://community.cronusmax.com)
- **Video Tutorials**: YouTube "Cronus Zen Setup"
- **Discord Community**: Cronus Official Discord

## ✅ Checkliste vor dem Spielen

- [ ] **Hardware** korrekt verbunden
- [ ] **Script** erfolgreich übertragen
- [ ] **Features** getestet
- [ ] **Einstellungen** angepasst
- [ ] **Konsolen-Settings** optimiert
- [ ] **The Division 2** gestartet
- [ ] **Ethische Nutzung** beachtet

---

**Viel Erfolg beim Setup! 🎮**

*Bei weiteren Fragen konsultieren Sie die Community oder den Support.*
