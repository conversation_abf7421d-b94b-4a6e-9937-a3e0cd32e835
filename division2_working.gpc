/*
 * The Division 2 - Complete Cronus Zen Script
 * Version: 2.2 (Working)
 * Author: Cronus Community
 *
 * Features:
 * - Anti-Recoil (Vertikal & Horizontal)
 * - Rapid Fire für Semi-Auto Waffen
 * - Aim Assist Verstärkung
 * - Auto Sprint
 * - Drop Shot / Jump Shot
 * - Quick Reload
 * - 4 Waffen-Profile
 */

// ===== VARIABLEN =====
int anti_recoil_active = FALSE;
int rapid_fire_active = FALSE;
int aim_assist_active = FALSE;
int auto_sprint_active = FALSE;
int strafe_active = FALSE;
int weapon_profile = 0;
int recoil_timer = 0;
int rapid_fire_timer = 0;
int strafe_timer = 0;
int strafe_direction = 1;

// Menü System
int menu_active = FALSE;
int menu_selection = 0;
int menu_timer = 0;
int setting_mode = FALSE;
int setting_value = 0;

// Waffen-Profile Werte
int vertical_recoil_ar = 35;
int vertical_recoil_smg = 25;
int vertical_recoil_lmg = 45;
int vertical_recoil_marksman = 20;

int horizontal_recoil_ar = 15;
int horizontal_recoil_smg = 20;
int horizontal_recoil_lmg = 25;
int horizontal_recoil_marksman = 10;

int fire_rate_ar = 12;
int fire_rate_smg = 15;
int fire_rate_lmg = 8;
int fire_rate_marksman = 6;

// Aktuelle Werte
int current_vertical = 35;
int current_horizontal = 15;
int current_fire_rate = 12;

// Strafe/Zick-Zack Einstellungen
int strafe_speed = 100;
int strafe_delay = 150;
int strafe_intensity = 80;

// ===== HAUPT SCRIPT =====
main {
    // MENÜ SYSTEM - L2 + Options
    if(event_press(PS4_L2) && get_val(PS4_OPTIONS)) {
        menu_active = !menu_active;
        if(menu_active) {
            menu_selection = 0;
            combo_run(menu_open);
        } else {
            combo_run(menu_close);
        }
    }

    // Menü Navigation (wenn aktiv)
    if(menu_active) {
        handle_menu_navigation();
        return; // Stoppe andere Funktionen während Menü aktiv ist
    }

    // Profile wechseln mit D-Pad Links + Rechts
    if(event_press(PS4_LEFT) && get_val(PS4_RIGHT)) {
        weapon_profile++;
        if(weapon_profile > 3) weapon_profile = 0;

        // Setze Werte basierend auf Profil
        if(weapon_profile == 0) { // AR
            current_vertical = vertical_recoil_ar;
            current_horizontal = horizontal_recoil_ar;
            current_fire_rate = fire_rate_ar;
        } else if(weapon_profile == 1) { // SMG
            current_vertical = vertical_recoil_smg;
            current_horizontal = horizontal_recoil_smg;
            current_fire_rate = fire_rate_smg;
        } else if(weapon_profile == 2) { // LMG
            current_vertical = vertical_recoil_lmg;
            current_horizontal = horizontal_recoil_lmg;
            current_fire_rate = fire_rate_lmg;
        } else if(weapon_profile == 3) { // Marksman
            current_vertical = vertical_recoil_marksman;
            current_horizontal = horizontal_recoil_marksman;
            current_fire_rate = fire_rate_marksman;
        }

        combo_run(profile_feedback);
    }

    // Anti-Recoil Toggle (L3 + R3)
    if(event_press(PS4_L3) && get_val(PS4_R3)) {
        anti_recoil_active = !anti_recoil_active;
        if(anti_recoil_active) {
            combo_run(feedback_on);
        } else {
            combo_run(feedback_off);
        }
    }

    // Rapid Fire Toggle (D-Pad Up + Down)
    if(event_press(PS4_UP) && get_val(PS4_DOWN)) {
        rapid_fire_active = !rapid_fire_active;
        combo_run(feedback_toggle);
    }

    // Aim Assist Toggle (L1 + R1)
    if(event_press(PS4_L1) && get_val(PS4_R1)) {
        aim_assist_active = !aim_assist_active;
        combo_run(feedback_toggle);
    }

    // Auto Sprint Toggle (Touchpad + Share)
    if(event_press(PS4_TOUCH) && get_val(PS4_SHARE)) {
        auto_sprint_active = !auto_sprint_active;
        combo_run(feedback_toggle);
    }

    // Strafe/Zick-Zack Toggle (L1 + L3)
    if(event_press(PS4_L1) && get_val(PS4_L3)) {
        strafe_active = !strafe_active;
        combo_run(feedback_toggle);
    }

    // Anti-Recoil System
    if(get_val(PS4_R2) > 50 && anti_recoil_active) {
        if(recoil_timer <= 0) {
            // Vertikaler Rückstoß
            set_val(PS4_RY, get_val(PS4_RY) + current_vertical);

            // Horizontaler Rückstoß (zufällig)
            if(abs(get_val(PS4_RX)) < 20) {
                set_val(PS4_RX, get_val(PS4_RX) + current_horizontal);
            }
            recoil_timer = 50;
        }
    }

    // Rapid Fire
    if(get_val(PS4_R2) > 50 && rapid_fire_active) {
        if(rapid_fire_timer <= 0) {
            set_val(PS4_R2, 0);
            rapid_fire_timer = current_fire_rate;
        }
    }

    // Aim Assist Verstärkung
    if(get_val(PS4_L2) > 50 && aim_assist_active) {
        if(abs(get_val(PS4_RX)) > 10 || abs(get_val(PS4_RY)) > 10) {
            set_val(PS4_RX, get_val(PS4_RX) * 125 / 100);
            set_val(PS4_RY, get_val(PS4_RY) * 125 / 100);
        }
    }

    // Auto Sprint
    if(auto_sprint_active && abs(get_val(PS4_LY)) > 85) {
        set_val(PS4_L3, 100);
    }

    // Strafe/Zick-Zack Movement
    if(strafe_active && get_val(PS4_L2) > 50) {
        if(strafe_timer <= 0) {
            if(strafe_direction == 1) {
                set_val(PS4_LX, strafe_intensity);
                strafe_direction = -1;
            } else {
                set_val(PS4_LX, -strafe_intensity);
                strafe_direction = 1;
            }
            strafe_timer = strafe_delay;
        }
    }

    // Drop Shot
    if(get_val(PS4_R2) > 50 && event_press(PS4_CIRCLE)) {
        combo_run(drop_shot_combo);
    }

    // Jump Shot
    if(get_val(PS4_R2) > 50 && event_press(PS4_CROSS)) {
        combo_run(jump_shot_combo);
    }

    // Quick Reload
    if(event_press(PS4_SQUARE)) {
        combo_run(quick_reload_combo);
    }

    // Timer Updates
    if(recoil_timer > 0) recoil_timer = recoil_timer - get_rtime();
    if(rapid_fire_timer > 0) rapid_fire_timer = rapid_fire_timer - get_rtime();
}

// ===== COMBOS =====

combo drop_shot_combo {
    set_val(PS4_CIRCLE, 100);
    wait(50);
    set_val(PS4_CIRCLE, 0);
    wait(100);
    set_val(PS4_R2, 100);
    wait(200);
    set_val(PS4_R2, 0);
}

combo jump_shot_combo {
    set_val(PS4_CROSS, 100);
    wait(50);
    set_val(PS4_CROSS, 0);
    wait(150);
    set_val(PS4_R2, 100);
    wait(200);
    set_val(PS4_R2, 0);
}

combo quick_reload_combo {
    set_val(PS4_SQUARE, 100);
    wait(50);
    set_val(PS4_SQUARE, 0);
    wait(50);
    set_val(PS4_SQUARE, 100);
    wait(50);
    set_val(PS4_SQUARE, 0);
}

combo feedback_on {
    set_rumble(RUMBLE_A, 100);
    wait(200);
    set_rumble(RUMBLE_A, 0);
    wait(100);
    set_rumble(RUMBLE_A, 100);
    wait(200);
    set_rumble(RUMBLE_A, 0);
}

combo feedback_off {
    set_rumble(RUMBLE_A, 100);
    wait(500);
    set_rumble(RUMBLE_A, 0);
}

combo feedback_toggle {
    set_rumble(RUMBLE_A, 100);
    wait(100);
    set_rumble(RUMBLE_A, 0);
}

combo profile_feedback {
    set_rumble(RUMBLE_A, 50);
    wait(100);
    set_rumble(RUMBLE_A, 0);
}
